'use client';

import React from "react";
import {
  FaSignOutAlt,
} from "react-icons/fa";
import { IoHome } from "react-icons/io5";


const SidebarItem = ({ icon: Icon, label }) => {
  return (
    <div className="flex items-center gap-3 w-full px-4 py-2 font-medium text-sm">
       <div className="w-8 h-8 flex items-center justify-center rounded-lg bg-orange-600 text-white">
         <Icon size={16} />
       </div>
       <span className="text-gray-600 font-semibold">
        {label}
        </span>
    </div>
  );
};


const Sidebar = ({ onLogout }) => {
  return (
    <div className="h-full w-64 bg-white shadow-lg flex flex-col justify-between rounded-2xl">

      <div>
        {/* sidebar header section */}
        <div className="flex justify-center mt-5 gap-3">
          <div className="text-end font-bold text-gray-600 bg-white">MBRL <br />Admin Portal</div>
          <div className="flex items-center justify-center">
            <img src="/brandLogo.png" alt="Logo" className="h-10" />
          </div>
        </div>

        <div className="h-[1px] w-full bg-gradient-to-r from-transparent via-gray-300 to-transparent mb-5 mt-3" />

        {/* sidebar menu section */}
        <nav className="px-2 space-y-1">
          <SidebarItem icon={IoHome} label="Dashboard" to="/dashboard" />
          <SidebarItem icon={IoHome} label="Library" to="/dashboard" />
          <SidebarItem icon={IoHome} label="Analytics" to="/dashboard" />
          <SidebarItem icon={IoHome} label="Users" to="/dashboard" />
        </nav>

        <section className="text-center mt-10 text-gray-600 font-semibold">
          Account Pages
        </section>

        <div className="h-[1px] w-full bg-gradient-to-r from-transparent via-gray-300 to-transparent mb-5 mt-3" />

        <nav className="px-2 space-y-1">
          <SidebarItem icon={IoHome} label="Profile" to="/dashboard" />
          <SidebarItem icon={FaSignOutAlt} label="Log Out" to="/dashboard" />
        </nav>

      </div>

      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={onLogout}
          className="flex items-center w-full gap-2 text-red-500 hover:text-red-600 transition"
        >
          <FaSignOutAlt />
          Logout
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
